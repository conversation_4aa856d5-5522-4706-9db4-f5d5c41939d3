/**
 * نظام تسجيل الأنشطة (Activity Logger)
 * يقوم بتسجيل جميع العمليات التي يقوم بها المستخدمون في النظام
 */

const mysql = require('mysql2');

// إعدادات قاعدة البيانات (نفس الإعدادات المستخدمة في server.js)
let dbConfig = {
  host: "localhost",
  user: "root",
  password: "Hbkhbkhbk@123",
  database: "hassan",
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// إنشاء connection pool
let pool = mysql.createPool(dbConfig);

/**
 * تسجيل نشاط جديد في قاعدة البيانات
 * @param {Object} logData - بيانات النشاط المراد تسجيله
 * @param {number|null} logData.user_id - معرف المستخدم (اختياري)
 * @param {string} logData.username - اسم المستخدم
 * @param {string} logData.action_type - نوع العملية (add, edit, delete, login, logout, print, deliver, return)
 * @param {string} logData.module - القسم (employees, vacations, custody, contributions, rewards, penalties, etc.)
 * @param {string|number|null} logData.record_id - معرف السجل المتأثر (اختياري)
 * @param {string} logData.message - وصف الإجراء
 * @returns {Promise<boolean>} - true إذا تم التسجيل بنجاح، false في حالة الخطأ
 */
async function logAction(logData) {
  try {
    // التحقق من البيانات المطلوبة
    if (!logData.username || !logData.action_type || !logData.module || !logData.message) {
      console.error('Activity Logger: البيانات المطلوبة مفقودة');
      return false;
    }

    // إعداد الاستعلام
    const query = `
      INSERT INTO activity_logs (user_id, username, action_type, module, record_id, message, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `;

    const values = [
      logData.user_id || null,
      logData.username,
      logData.action_type,
      logData.module,
      logData.record_id || null,
      logData.message
    ];

    // تنفيذ الاستعلام
    await pool.promise().query(query, values);
    
    console.log(`Activity logged: ${logData.username} - ${logData.action_type} - ${logData.module}`);
    return true;

  } catch (error) {
    console.error('خطأ في تسجيل النشاط:', error);
    return false;
  }
}

/**
 * الحصول على سجل الأنشطة مع إمكانية التصفية
 * @param {Object} filters - فلاتر البحث
 * @param {string|null} filters.username - اسم المستخدم
 * @param {string|null} filters.action_type - نوع العملية
 * @param {string|null} filters.module - القسم
 * @param {string|null} filters.date_from - تاريخ البداية (YYYY-MM-DD)
 * @param {string|null} filters.date_to - تاريخ النهاية (YYYY-MM-DD)
 * @param {number} filters.limit - عدد السجلات المطلوبة (افتراضي: 100)
 * @param {number} filters.offset - نقطة البداية (افتراضي: 0)
 * @returns {Promise<Array>} - مصفوفة السجلات
 */
async function getActivityLogs(filters = {}) {
  try {
    let query = `
      SELECT 
        id,
        user_id,
        username,
        action_type,
        module,
        record_id,
        message,
        created_at,
        created_at,
        DATE_FORMAT(created_at, '%d/%m/%Y %H:%i:%s') as formatted_date
      FROM activity_logs
      WHERE 1=1
    `;
    
    const values = [];

    // إضافة الفلاتر
    if (filters.username) {
      query += ` AND username LIKE ?`;
      values.push(`%${filters.username}%`);
    }

    if (filters.action_type) {
      query += ` AND action_type = ?`;
      values.push(filters.action_type);
    }

    if (filters.module) {
      query += ` AND module = ?`;
      values.push(filters.module);
    }

    if (filters.date_from) {
      query += ` AND DATE(created_at) >= ?`;
      values.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ` AND DATE(created_at) <= ?`;
      values.push(filters.date_to);
    }

    // ترتيب النتائج
    query += ` ORDER BY created_at DESC`;

    // إضافة الحد الأقصى للنتائج
    const limit = parseInt(filters.limit) || 100;
    const offset = parseInt(filters.offset) || 0;
    query += ` LIMIT ? OFFSET ?`;
    values.push(limit, offset);

    const [rows] = await pool.promise().query(query, values);

    // تحويل التاريخ إلى نظام 12 ساعة عربي
    return rows.map(log => ({
      ...log,
      formatted_date: formatDateFor12Hour(log.created_at)
    }));

  } catch (error) {
    console.error('خطأ في جلب سجل الأنشطة:', error);
    return [];
  }
}

/**
 * الحصول على إحصائيات سجل الأنشطة
 * @param {Object} filters - فلاتر البحث (نفس فلاتر getActivityLogs)
 * @returns {Promise<Object>} - إحصائيات السجل
 */
async function getActivityStats(filters = {}) {
  try {
    let query = `
      SELECT 
        COUNT(*) as total_count,
        COUNT(DISTINCT username) as unique_users,
        COUNT(DISTINCT module) as unique_modules,
        COUNT(DISTINCT action_type) as unique_actions
      FROM activity_logs
      WHERE 1=1
    `;
    
    const values = [];

    // إضافة نفس الفلاتر
    if (filters.username) {
      query += ` AND username LIKE ?`;
      values.push(`%${filters.username}%`);
    }

    if (filters.action_type) {
      query += ` AND action_type = ?`;
      values.push(filters.action_type);
    }

    if (filters.module) {
      query += ` AND module = ?`;
      values.push(filters.module);
    }

    if (filters.date_from) {
      query += ` AND DATE(created_at) >= ?`;
      values.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ` AND DATE(created_at) <= ?`;
      values.push(filters.date_to);
    }

    const [rows] = await pool.promise().query(query, values);
    return rows[0] || { total_count: 0, unique_users: 0, unique_modules: 0, unique_actions: 0 };

  } catch (error) {
    console.error('خطأ في جلب إحصائيات الأنشطة:', error);
    return { total_count: 0, unique_users: 0, unique_modules: 0, unique_actions: 0 };
  }
}

/**
 * التحقق من وجود تغييرات فعلية بين البيانات القديمة والجديدة
 * @param {Object} oldData - البيانات القديمة
 * @param {Object} newData - البيانات الجديدة
 * @returns {boolean} - true إذا كانت هناك تغييرات فعلية، false إذا لم تكن هناك تغييرات
 */
function hasActualChanges(oldData, newData) {
  try {
    // معالجة مسبقة للبيانات القديمة لتحويل Date objects إلى strings منسقة
    const processedOldData = {};
    for (const [key, value] of Object.entries(oldData)) {
      if (value instanceof Date) {
        // استخدام نفس منطق formatValue للتواريخ لضمان التطابق
        if (key === 'vacation_date' || key.includes('date') || key.includes('Date')) {
          const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];

          const day = value.getDate();
          const month = arabicMonths[value.getMonth()];
          const year = value.getFullYear();

          processedOldData[key] = `${day} ${month} ${year}`;
        } else {
          // للحقول الأخرى، استخدم التنسيق الكامل
          processedOldData[key] = value.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
        }
      } else {
        processedOldData[key] = value;
      }
    }

    // مقارنة الحقول للتحقق من وجود تغييرات
    for (const [key, newValue] of Object.entries(newData)) {
      // تجاهل الحقول الفنية
      if (['id', 'created_at', 'updated_at'].includes(key)) {
        continue;
      }

      // التحقق من وجود الحقل في البيانات القديمة
      if (!oldData.hasOwnProperty(key)) {
        continue;
      }

      const oldValue = processedOldData[key];

      // تنسيق القيم مع تمرير اسم الحقل للتنسيق المناسب
      const formattedOldValue = formatValue(oldValue, key);
      const formattedNewValue = formatValue(newValue, key);

      // مقارنة القيم المنسقة بدلاً من القيم الخام
      // التأكد من أن القيم مختلفة فعلياً وليست null أو undefined
      if (formattedOldValue !== formattedNewValue &&
          newValue !== null && newValue !== undefined && newValue !== '') {

        // مقارنة إضافية للقيم الخام لتجنب التغييرات الوهمية
        let actuallyChanged = false;

        // معالجة خاصة للتواريخ
        if (key === 'vacation_date' || key.includes('date') || key.includes('Date')) {
          let oldDateStr = '';
          let newDateStr = '';

          // معالجة التاريخ القديم
          if (oldValue instanceof Date) {
            oldDateStr = oldValue.toISOString().split('T')[0]; // YYYY-MM-DD
          } else if (typeof oldValue === 'string' && oldValue.match(/^\d{4}-\d{2}-\d{2}/)) {
            oldDateStr = oldValue.split('T')[0]; // أخذ الجزء الأول فقط
          } else if (typeof oldValue === 'string') {
            oldDateStr = oldValue;
          }

          // معالجة التاريخ الجديد
          if (newValue instanceof Date) {
            newDateStr = newValue.toISOString().split('T')[0]; // YYYY-MM-DD
          } else if (typeof newValue === 'string' && newValue.match(/^\d{4}-\d{2}-\d{2}/)) {
            newDateStr = newValue.split('T')[0]; // أخذ الجزء الأول فقط
          } else if (typeof newValue === 'string') {
            newDateStr = newValue;
          }

          actuallyChanged = oldDateStr !== newDateStr;
        } else if (typeof oldValue === 'number' && typeof newValue === 'number') {
          // للأرقام، نقارن القيم الرقمية مباشرة
          actuallyChanged = parseFloat(oldValue) !== parseFloat(newValue);
        } else {
          // للحقول الأخرى، نقارن القيم الخام أولاً
          actuallyChanged = String(oldValue).trim() !== String(newValue).trim();
        }

        if (actuallyChanged) {
          return true; // وجدت تغيير فعلي
        }
      }
    }

    return false; // لا توجد تغييرات فعلية

  } catch (error) {
    console.error('خطأ في التحقق من التغييرات:', error);
    return true; // في حالة الخطأ، نفترض وجود تغييرات لتجنب منع التحديثات المهمة
  }
}

/**
 * إنشاء رسالة تعديل مفصلة تظهر التغييرات قبل وبعد التعديل
 * @param {string} entityName - اسم الكيان (موظف، إجازة، إلخ)
 * @param {Object} oldData - البيانات القديمة
 * @param {Object} newData - البيانات الجديدة
 * @param {Object} fieldLabels - تسميات الحقول باللغة العربية
 * @returns {string} - رسالة التعديل المفصلة
 */
function createEditMessage(entityName, oldData, newData, fieldLabels = {}) {
  try {
    const changes = [];

    // معالجة مسبقة للبيانات القديمة لتحويل Date objects إلى strings منسقة
    const processedOldData = {};
    for (const [key, value] of Object.entries(oldData)) {
      if (value instanceof Date) {
        // استخدام نفس منطق formatValue للتواريخ لضمان التطابق
        if (key === 'vacation_date' || key.includes('date') || key.includes('Date') || key.includes('period')) {
          const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];

          const day = value.getDate();
          const month = arabicMonths[value.getMonth()];
          const year = value.getFullYear();

          processedOldData[key] = `${day} ${month} ${year}`;
        } else {
          // للحقول الأخرى، استخدم التنسيق الكامل
          processedOldData[key] = value.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
        }
      } else {
        processedOldData[key] = value;
      }
    }

    // مقارنة الحقول وإنشاء قائمة التغييرات
    for (const [key, newValue] of Object.entries(newData)) {
      // تجاهل الحقول الفنية
      if (['id', 'created_at', 'updated_at'].includes(key)) {
        continue;
      }

      // التحقق من وجود الحقل في البيانات القديمة
      if (!oldData.hasOwnProperty(key)) {
        continue;
      }

      const oldValue = processedOldData[key];

      // الحصول على تسمية الحقل باللغة العربية
      const fieldLabel = fieldLabels[key] || key;

      // تنسيق القيم مع تمرير اسم الحقل للتنسيق المناسب
      const formattedOldValue = formatValue(oldValue, key);
      const formattedNewValue = formatValue(newValue, key);

      // مقارنة القيم المنسقة بدلاً من القيم الخام
      // التأكد من أن القيم مختلفة فعلياً وليست null أو undefined
      if (formattedOldValue !== formattedNewValue &&
          newValue !== null && newValue !== undefined && newValue !== '') {

        // مقارنة إضافية للقيم الخام لتجنب التغييرات الوهمية
        let actuallyChanged = false;

        // للتواريخ، نقارن التواريخ بصيغة YYYY-MM-DD
        if (key === 'vacation_date' || key.includes('date') || key.includes('period')) {
          // تحويل التواريخ إلى صيغة موحدة للمقارنة
          let oldDateStr = '';
          let newDateStr = '';

          // معالجة التاريخ القديم
          if (oldValue instanceof Date) {
            oldDateStr = oldValue.toISOString().split('T')[0]; // YYYY-MM-DD
          } else if (typeof oldValue === 'string' && oldValue.match(/^\d{4}-\d{2}-\d{2}/)) {
            oldDateStr = oldValue.split('T')[0]; // أخذ الجزء الأول فقط
          } else if (typeof oldValue === 'string') {
            oldDateStr = oldValue;
          }

          // معالجة التاريخ الجديد
          if (newValue instanceof Date) {
            newDateStr = newValue.toISOString().split('T')[0]; // YYYY-MM-DD
          } else if (typeof newValue === 'string' && newValue.match(/^\d{4}-\d{2}-\d{2}/)) {
            newDateStr = newValue.split('T')[0]; // أخذ الجزء الأول فقط
          } else if (typeof newValue === 'string') {
            newDateStr = newValue;
          }

          actuallyChanged = oldDateStr !== newDateStr;
        } else if (typeof oldValue === 'number' && typeof newValue === 'number') {
          // للأرقام، نقارن القيم الرقمية مباشرة
          actuallyChanged = parseFloat(oldValue) !== parseFloat(newValue);
        } else {
          // للحقول الأخرى، نقارن القيم الخام أولاً
          actuallyChanged = String(oldValue).trim() !== String(newValue).trim();
        }

        if (actuallyChanged) {
          changes.push(`${fieldLabel}: من "${formattedOldValue}" إلى "${formattedNewValue}"`);
        }
      }
    }

    if (changes.length === 0) {
      return `تم تعديل ${entityName} (لا توجد تغييرات مرئية)`;
    }

    return `تم تعديل ${entityName} - التغييرات: ${changes.join(' | ')}`;

  } catch (error) {
    console.error('خطأ في إنشاء رسالة التعديل:', error);
    return `تم تعديل ${entityName}`;
  }
}

/**
 * تنسيق التاريخ لنظام 12 ساعة عربي لجدول سجل الأنشطة
 * @param {Date} date - التاريخ المراد تنسيقه
 * @returns {string} - التاريخ المنسق بنظام 12 ساعة
 */
function formatDateFor12Hour(date) {
  if (!date) return '';

  try {
    const dateObj = new Date(date);

    // التحقق من صحة التاريخ
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // تنسيق مبسط وواضح للتاريخ والوقت
    const arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const day = dateObj.getDate();
    const month = arabicMonths[dateObj.getMonth()];
    const year = dateObj.getFullYear();

    // تنسيق الوقت بنظام 12 ساعة
    let hours = dateObj.getHours();
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'م' : 'ص';

    if (hours === 0) {
      hours = 12;
    } else if (hours > 12) {
      hours = hours - 12;
    }

    return `${day} ${month} ${year} - ${hours}:${minutes} ${ampm}`;

  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return '';
  }
}

/**
 * تنسيق القيم للعرض
 * @param {any} value - القيمة المراد تنسيقها
 * @param {string} fieldName - اسم الحقل لتطبيق التنسيق المناسب
 * @returns {string} - القيمة المنسقة
 */
function formatValue(value, fieldName = '') {
  if (value === null || value === undefined || value === '') {
    // معالجة خاصة لحقل نوع الإجازة الرسمية
    if (fieldName === 'official_type') {
      return 'لا يوجد';
    }
    return 'غير محدد';
  }

  if (typeof value === 'boolean') {
    return value ? 'نعم' : 'لا';
  }

  if (typeof value === 'number') {
    // تنسيق الأرقام بدون أصفار عشرية غير ضرورية
    if (value % 1 === 0) {
      return parseInt(value).toString();
    }
    return value.toString();
  }

  // معالجة الأرقام التي تأتي كـ strings من قاعدة البيانات
  if (typeof value === 'string' && !isNaN(parseFloat(value)) && isFinite(value)) {
    // التحقق من أن هذا حقل رقمي (مبلغ، نقاط، ساعات، إلخ)
    if (fieldName.includes('amount') || fieldName.includes('score') || fieldName.includes('hours') ||
        fieldName.includes('cost') || fieldName.includes('salary') || fieldName.includes('reward') ||
        fieldName === 'advance_amount' || fieldName === 'evaluation_score' || fieldName === 'extra_hours' ||
        fieldName === 'company_amount' || fieldName === 'fund_amount') {

      const numValue = parseFloat(value);
      if (numValue % 1 === 0) {
        return parseInt(numValue).toString();
      }
      return numValue.toString();
    }
  }

  // تنسيق التواريخ (Date objects)
  if (value instanceof Date) {
    // للتواريخ، نستخدم نفس التنسيق المستخدم للنصوص لضمان التطابق
    if (fieldName === 'vacation_date' || fieldName.includes('date') || fieldName.includes('Date') || fieldName.includes('period')) {
      const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];

      const day = value.getDate();
      const month = arabicMonths[value.getMonth()];
      const year = value.getFullYear();

      return `${day} ${month} ${year}`;
    }

    // للحقول الأخرى، استخدم التنسيق الكامل
    return value.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  if (typeof value === 'string') {
    // تنسيق التواريخ النصية
    if (fieldName === 'vacation_date' || fieldName.includes('date') || fieldName.includes('Date') || fieldName.includes('period')) {
      try {
        // التعامل مع التواريخ بصيغة YYYY-MM-DD
        if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
          const [year, month, day] = value.split('-');
          const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];
          const monthName = arabicMonths[parseInt(month) - 1];
          return `${parseInt(day)} ${monthName} ${year}`;
        }

        // التعامل مع التواريخ الأخرى بحذر لتجنب مشاكل timezone
        const date = new Date(value + 'T00:00:00');
        if (!isNaN(date.getTime())) {
          // أسماء الأشهر بالعربية
          const arabicMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
          ];

          const day = date.getDate();
          const month = arabicMonths[date.getMonth()];
          const year = date.getFullYear();

          return `${day} ${month} ${year}`;
        }
      } catch (error) {
        // إذا فشل تحويل التاريخ، نعيد القيمة الأصلية
      }
    }

    // تنسيق أنواع الإجازات
    if (fieldName === 'vacation_type' || fieldName === 'official_type') {
      const vacationTypeMap = {
        // أنواع الإجازات الأساسية
        'casual': 'إجازة عارضة',
        'permission': 'غياب بإذن',
        'absence': 'غياب بدون إذن',
        'annual': 'إجازة سنوية',
        'unpaid': 'إجازة بدون راتب',
        'sick': 'إجازة مرضية',
        'official': 'إجازات خارج الرصيد',

        // الإجازات خارج الرصيد الموجودة
        'police_day': 'عيد الشرطة (25 يناير)',
        'sinai_day': 'عيد تحرير سيناء',
        'labor_day': 'عيد العمال',
        'june_revolution': 'عيد ثورة 30 يونيو',
        'july_revolution': 'عيد ثورة 23 يوليو',
        'armed_forces_day': 'عيد القوات المسلحة (6 أكتوبر)',
        'christmas': 'عيد الميلاد المجيد',
        'sham_el_nessim': 'عيد شم النسيم',
        'islamic_new_year': 'رأس السنة الهجرية',
        'prophet_birthday': 'المولد النبوي الشريف',
        'eid_adha': 'عيد الأضحى',
        'eid_fitr': 'عيد الفطر',
        // الأنواع الجديدة المضافة بعد عيد الفطر
        'emergency': 'اجازة اضطرارية',
        'birth': 'مولود',
        'maternity': 'اجازة وضع',
        'marriage': 'زواج',
        'death_first_degree': 'الوفاة من الدرجة الاولى',
        'military_service': 'الاستدعاء للجيش',
        'exams': 'الامتحانات',
        'pilgrimage': 'الحج والعمرة',
        // الأنواع القديمة للتوافق مع البيانات الموجودة
        'new_year': 'رأس السنة الميلادية',
        'coptic_christmas': 'عيد الميلاد المجيد',
        'january_revolution': 'ثورة 25 يناير',
        'sinai_liberation': 'تحرير سيناء',
        'arafat_day': 'يوم عرفة',
        'eid_al_adha': 'عيد الأضحى المبارك',
        'eid_al_adha_2': 'عيد الأضحى المبارك (اليوم الثاني)',
        'eid_al_adha_3': 'عيد الأضحى المبارك (اليوم الثالث)',
        'eid_al_fitr': 'عيد الفطر المبارك',
        'eid_al_fitr_2': 'عيد الفطر المبارك (اليوم الثاني)',
        'eid_al_fitr_3': 'عيد الفطر المبارك (اليوم الثالث)',
        'other': 'أخرى'
      };

      return vacationTypeMap[value] || value;
    }

    // تنسيق أنواع المساهمات
    if (fieldName === 'contribution_type') {
      // إذا كان نوع المساهمة نصي (من القائمة المنسدلة) نعيده كما هو
      if (isNaN(parseInt(value))) {
        return value;
      }

      // إذا كان رقمي نستخدم القاموس
      const contributionTypeMap = {
        '1': 'الزواج',
        '2': 'إنجاب الطفل',
        '3': 'الولادة الطبيعية للزوجة',
        '4': 'الولادة القيصرية للزوجة',
        '5': 'إصابة العمل',
        '6': 'العمليات الجراحية للعامل الغير مؤمن عليه',
        '7': 'العمليات الجراحية للزوجة والأبناء',
        '8': 'المرضى دون الأمراض المزمنة',
        '9': 'الوفاة المؤمن عليه اجتماعيا',
        '10': 'وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا',
        '11': 'وفاة أحد الأقارب من الدرجة الأولى',
        '12': 'زواج أحد أبناء العاملين'
      };

      return contributionTypeMap[value] || value;
    }

    // تنسيق التواريخ النصية بصيغة YYYY-MM-DD
    if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return new Date(value + 'T12:00:00').toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    }

    // تنسيق التواريخ النصية الطويلة مثل "Wed Jul 09 2025 00:00:00 GMT+0300 (توقيت شرق أوروبا الصيفي)"
    if (value.match(/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\w+\s+\d{1,2}\s+\d{4}/)) {
      return new Date(value).toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    }

    // تنسيق التواريخ النصية بصيغة ISO مثل "2025-07-01T00:00:00.000Z"
    if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
      return formatDateFor12Hour(new Date(value));
    }

    // تنسيق أي نص يحتوي على GMT (للتأكد من التقاط جميع التواريخ الإنجليزية)
    if (value.includes('GMT') && value.match(/\d{4}/)) {
      try {
        return formatDateFor12Hour(new Date(value));
      } catch (e) {
        // في حالة فشل التحويل، نعيد النص كما هو
        console.warn('فشل في تحويل التاريخ:', value);
      }
    }

    // تنسيق التواريخ بصيغة JavaScript الافتراضية مثل "Fri Aug 01 2025..."
    if (value.match(/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\w+\s+\d{1,2}\s+\d{4}/)) {
      try {
        return formatDateFor12Hour(new Date(value));
      } catch (e) {
        console.warn('فشل في تحويل التاريخ:', value);
      }
    }

    return value.trim();
  }

  return String(value);
}

/**
 * حذف السجلات القديمة (تنظيف قاعدة البيانات)
 * @param {number} daysToKeep - عدد الأيام المراد الاحتفاظ بها (افتراضي: 90 يوم)
 * @returns {Promise<number>} - عدد السجلات المحذوفة
 */
async function cleanOldLogs(daysToKeep = 90) {
  try {
    const query = `
      DELETE FROM activity_logs
      WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    const [result] = await pool.promise().query(query, [daysToKeep]);

    console.log(`تم حذف ${result.affectedRows} سجل قديم من سجل الأنشطة`);
    return result.affectedRows;

  } catch (error) {
    console.error('خطأ في حذف السجلات القديمة:', error);
    return 0;
  }
}

// تصدير الدوال
module.exports = {
  logAction,
  getActivityLogs,
  getActivityStats,
  cleanOldLogs,
  createEditMessage,
  hasActualChanges
};

// أمثلة على الاستخدام:

/*
// مثال 1: تسجيل عملية حذف موظف
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'delete',
  module: 'employees',
  record_id: '12345',
  message: 'تم حذف الموظف: أحمد محمد علي (كود: 12345)'
});

// مثال 2: تسجيل عملية تسجيل دخول
await logAction({
  username: 'admin',
  action_type: 'login',
  module: 'system',
  message: 'تسجيل دخول المستخدم إلى النظام'
});

// مثال 3: تسجيل عملية تسليم عهدة
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'deliver',
  module: 'custody',
  record_id: 'OP001',
  message: 'تم تسليم عهدة: لابتوب ديل (كود: LAP001) للموظف: أحمد محمد (كود: 12345)'
});

// مثال 4: تسجيل عملية طباعة تقرير
await logAction({
  user_id: 1,
  username: 'admin',
  action_type: 'print',
  module: 'vacations',
  message: 'تم طباعة تقرير الإجازات للفترة من 2024-01-01 إلى 2024-01-31'
});
*/
