const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { addDepartmentFilter } = require("../middleware/departmentFilter");
const { logAction, createEditMessage, hasActualChanges } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

const router = express.Router();

console.log('📚 تم تحميل routes/evaluations.js');

/**
 * دالة لتنسيق التاريخ بصيغة dd/mm/yyyy
 * @param {string|Date} dateString - التاريخ المراد تنسيقه
 * @returns {string} - التاريخ المنسق
 */
function formatDateForDisplay(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${day}/${month}/${year}`;
}

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات التقييمات
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToEvaluations(whereClause, queryParams, req) {
  console.log(`🔍 Evaluations Filter - allowedDepartments:`, req.allowedDepartments);

  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    console.log(`👑 Admin user - no filtering applied to evaluations`);
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    const additionalWhere = ` AND emp.department IN (${departmentPlaceholders}) AND emp.department IS NOT NULL`;
    whereClause = whereClause ? whereClause + additionalWhere : `WHERE 1=1${additionalWhere}`;
    queryParams.push(...req.allowedDepartments);
    console.log(`✅ Evaluations filter applied for departments:`, req.allowedDepartments);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    const additionalWhere = ` AND 1 = 0`;
    whereClause = whereClause ? whereClause + additionalWhere : `WHERE 1 = 0`;
    console.log(`🚫 No departments allowed - blocking all evaluations data`);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  }
}

// اختبار بسيط
router.get('/test', (req, res) => {
  console.log('🧪 تم استدعاء /evaluations/test');
  res.json({ message: 'Evaluations route is working!' });
});

// DataTables server-side processing للتقييمات
router.get('/datatables', authenticateToken, addDepartmentFilter, checkPermission('view_evaluation'), async (req, res) => {
  console.log('🔥 تم استدعاء /evaluations/datatables');
  try {
    // استخدام pool المستورد مباشرة

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'e.employee_code',
      'emp.full_name',
      'emp.department',
      'e.evaluation_type',
      'e.start_date',
      'e.end_date',
      'e.score',
      'e.notes'
    ];

    const orderColumn = columns[orderColumnIndex] || 'e.created_at';

    // بناء استعلام البحث
    let whereClause = '';
    let searchParams = [];

    if (searchValue) {
      whereClause = `WHERE (
        e.employee_code LIKE ? OR
        emp.full_name LIKE ? OR
        emp.department LIKE ? OR
        e.evaluation_type LIKE ? OR
        e.notes LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams = [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern];
    }

    // البحث في الأعمدة المحددة (للفلاتر المتقدمة)
    const columnSearches = [];
    const columnParams = [];

    // البحث بكود الموظف
    if (req.query.columns?.[0]?.search?.value) {
      columnSearches.push('e.employee_code LIKE ?');
      columnParams.push(`%${req.query.columns[0].search.value}%`);
    }

    // البحث باسم الموظف
    if (req.query.columns?.[1]?.search?.value) {
      columnSearches.push('emp.full_name LIKE ?');
      columnParams.push(`%${req.query.columns[1].search.value}%`);
    }

    // البحث بنوع التقييم
    if (req.query.columns?.[3]?.search?.value) {
      columnSearches.push('e.evaluation_type LIKE ?');
      columnParams.push(`%${req.query.columns[3].search.value}%`);
    }

    // فلاتر الدرجات
    if (req.query.minScore) {
      columnSearches.push('e.score >= ?');
      columnParams.push(req.query.minScore);
    }

    if (req.query.maxScore) {
      columnSearches.push('e.score <= ?');
      columnParams.push(req.query.maxScore);
    }

    // فلاتر التاريخ
    if (req.query.dateFrom) {
      columnSearches.push('DATE(e.start_date) >= ?');
      columnParams.push(req.query.dateFrom);
    }

    if (req.query.dateTo) {
      columnSearches.push('DATE(e.start_date) <= ?');
      columnParams.push(req.query.dateTo);
    }

    // دمج شروط البحث
    if (columnSearches.length > 0) {
      if (whereClause) {
        whereClause += ' AND (' + columnSearches.join(' AND ') + ')';
        searchParams = searchParams.concat(columnParams);
      } else {
        whereClause = 'WHERE ' + columnSearches.join(' AND ');
        searchParams = columnParams;
      }
    }

    // إضافة فلترة الإدارات
    const departmentFilter = addDepartmentFilterToEvaluations(whereClause, searchParams, req);
    whereClause = departmentFilter.whereClause;
    searchParams = departmentFilter.queryParams;

    // حساب إجمالي السجلات مع فلترة الإدارات
    let totalQuery, totalParams;
    if (req.allowedDepartments === null) {
      totalQuery = 'SELECT COUNT(*) as total FROM evaluations e LEFT JOIN employees emp ON e.employee_code = emp.code';
      totalParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalQuery = `
        SELECT COUNT(*) as total
        FROM evaluations e
        INNER JOIN employees emp ON e.employee_code = emp.code
        WHERE emp.department IN (${departmentPlaceholders}) AND emp.department IS NOT NULL
      `;
      totalParams = req.allowedDepartments;
    } else {
      totalQuery = 'SELECT 0 as total';
      totalParams = [];
    }

    const [totalResult] = await pool.promise().query(totalQuery, totalParams);
    const totalRecords = totalResult[0].total;

    // حساب السجلات المفلترة
    const [filteredResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM evaluations e INNER JOIN employees emp ON e.employee_code = emp.code ${whereClause}`,
      searchParams
    );
    const filteredRecords = filteredResult[0].total;

    // جلب البيانات مع الترقيم والترتيب
    const joinType = req.allowedDepartments === null ? 'LEFT JOIN' : 'INNER JOIN';
    const [rows] = await pool.promise().query(
      `SELECT e.id, e.employee_code,
              COALESCE(emp.full_name, e.employee_name) as employee_name,
              COALESCE(emp.department, e.department) as department,
              e.evaluation_type,
              DATE_FORMAT(e.start_date, '%Y-%m-%d') as start_date_formatted,
              DATE_FORMAT(e.end_date, '%Y-%m-%d') as end_date_formatted,
              e.score, e.notes
       FROM evaluations e
       ${joinType} employees emp ON e.employee_code = emp.code
       ${whereClause}
       ORDER BY ${orderColumn} ${orderDirection}
       LIMIT ? OFFSET ?`,
      [...searchParams, length, start]
    );

    // تنسيق البيانات للعرض
    const formattedData = rows.map(row => {
      // تحديد لون الدرجة
      let scoreClass = '';
      const score = parseFloat(row.score);
      if (score >= 90) {
        scoreClass = 'score-excellent';
      } else if (score >= 80) {
        scoreClass = 'score-good';
      } else if (score >= 70) {
        scoreClass = 'score-average';
      } else {
        scoreClass = 'score-poor';
      }

      return [
        row.employee_code,
        row.employee_name || 'غير محدد',
        row.department || 'غير محدد',
        row.evaluation_type,
        row.start_date_formatted,
        row.end_date_formatted,
        `<span class="${scoreClass}">${row.score}%</span>`,
        row.notes ? `<span title="${row.notes}">${row.notes.substring(0, 50)}${row.notes.length > 50 ? '...' : ''}</span>` : 'لا توجد ملاحظات',
        `<button class="edit-evaluation-btn" data-evaluation-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-evaluation-btn" data-evaluation-id="${row.id}" data-permission="can_delete">حذف</button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables
    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('❌ خطأ في جلب بيانات DataTables للتقييمات:', error);
    console.error('📊 تفاصيل الخطأ:', error.message);
    console.error('📍 Stack trace:', error.stack);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      details: error.message,
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// الحصول على جميع التقييمات
router.get('/', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      ORDER BY e.created_at DESC, e.id DESC
    `);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب التقييمات:', error);
    res.status(500).json({ error: 'فشل في جلب التقييمات' });
  }
});

// الحصول على تقييم محدد
router.get('/:id', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { id } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE e.id = ?
    `, [id]);
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب التقييم:', error);
    res.status(500).json({ error: 'فشل في جلب التقييم' });
  }
});

// إضافة تقييم جديد
router.post('/', authenticateToken, checkPermission('can_add'), async (req, res) => {
  try {
    const {
      employee_code,
      evaluation_type,
      start_date,
      end_date,
      score,
      notes
    } = req.body;

    if (!employee_code || !evaluation_type || !start_date || !end_date || !score) {
      return res.status(400).json({ error: 'كود الموظف ونوع التقييم وتاريخ البداية والنهاية والدرجة مطلوبة' });
    }

    // التحقق من صحة نوع التقييم
    const validTypes = ['شهري', 'ربع سنوي'];
    if (!validTypes.includes(evaluation_type)) {
      return res.status(400).json({ error: 'نوع التقييم يجب أن يكون شهري أو ربع سنوي' });
    }
    
    // الحصول على بيانات الموظف
    const [employeeData] = await pool.promise().query(
      "SELECT full_name, department FROM employees WHERE code = ?",
      [employee_code]
    );

    if (employeeData.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const employee_name = employeeData[0].full_name;
    const department = employeeData[0].department;

    // التحقق من عدم وجود تقييم آخر لنفس الموظف في نفس الفترة الزمنية
    const [existingEvaluations] = await pool.promise().query(
      `SELECT id, evaluation_type, start_date, end_date
       FROM evaluations
       WHERE employee_code = ?
       AND (
         (start_date <= ? AND end_date >= ?) OR
         (start_date <= ? AND end_date >= ?) OR
         (start_date >= ? AND end_date <= ?)
       )`,
      [
        employee_code,
        start_date, start_date,  // التحقق من تداخل تاريخ البداية
        end_date, end_date,      // التحقق من تداخل تاريخ النهاية
        start_date, end_date     // التحقق من احتواء الفترة الجديدة للفترة الموجودة
      ]
    );

    if (existingEvaluations.length > 0) {
      const existingEval = existingEvaluations[0];

      const formattedStartDate = formatDateForDisplay(existingEval.start_date);
      const formattedEndDate = formatDateForDisplay(existingEval.end_date);

      return res.status(400).json({
        error: `يوجد تقييم ${existingEval.evaluation_type} للموظف ${employee_name} في فترة متداخلة من ${formattedStartDate} إلى ${formattedEndDate}. لا يمكن إضافة تقييم آخر في نفس الفترة الزمنية.`
      });
    }

    const [result] = await pool.promise().query(
      `INSERT INTO evaluations (
        employee_code, employee_name, department, evaluation_type,
        start_date, end_date, score, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        employee_code, employee_name, department, evaluation_type,
        start_date, end_date, score, notes
      ]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'evaluations',
      record_id: result.insertId.toString(),
      message: `تم إضافة تقييم ${evaluation_type} للموظف: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الدرجة: ${score} - الفترة: من ${start_date} إلى ${end_date}`
    });

    res.status(201).json({
      id: result.insertId,
      employee_code,
      employee_name,
      department,
      evaluation_type,
      start_date,
      end_date,
      score,
      message: 'تم إضافة التقييم بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة التقييم:', error);
    res.status(500).json({ error: 'فشل في إضافة التقييم' });
  }
});

// تحديث تقييم
router.put('/:id', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const { id } = req.params;
    // تنظيف البيانات المرسلة من العميل
    const updateData = cleanUpdateData(req.body);

    // التحقق من وجود التقييم والحصول على البيانات القديمة
    const [existingEvaluation] = await pool.promise().query(
      "SELECT * FROM evaluations WHERE id = ?",
      [id]
    );

    if (existingEvaluation.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }

    const oldData = existingEvaluation[0];

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.updated_at;

    // فلترة الحقول المسموحة فقط حسب الجدول الفعلي
    const allowedFields = [
      'employee_code', 'employee_name', 'department', 'evaluation_type',
      'start_date', 'end_date', 'score', 'notes'
    ];

    // إزالة أي حقول غير مسموحة
    Object.keys(updateData).forEach(key => {
      if (!allowedFields.includes(key)) {
        delete updateData[key];
      }
    });

    // التحقق من صحة نوع التقييم إذا تم تحديثه
    if (updateData.evaluation_type) {
      const validTypes = ['شهري', 'ربع سنوي'];
      if (!validTypes.includes(updateData.evaluation_type)) {
        return res.status(400).json({ error: 'نوع التقييم يجب أن يكون شهري أو ربع سنوي' });
      }
    }

    // إذا تم تغيير كود الموظف، تحديث اسم الموظف والقسم
    if (updateData.employee_code && updateData.employee_code !== oldData.employee_code) {
      const [employeeRows] = await pool.promise().query(
        "SELECT full_name, department FROM employees WHERE code = ?",
        [updateData.employee_code]
      );

      if (employeeRows.length > 0) {
        updateData.employee_name = employeeRows[0].full_name;
        updateData.department = employeeRows[0].department;
      }
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة
    const dateFields = [
      'start_date', 'end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // التحقق من وجود تغييرات فعلية
    if (!hasActualChanges(oldData, updateData)) {
      return res.status(400).json({ error: 'لا يوجد تغيير في البيانات' });
    }

    // التحقق من عدم وجود تقييم آخر في نفس الفترة الزمنية (إذا تم تغيير التواريخ أو كود الموظف)
    if (updateData.start_date || updateData.end_date || updateData.employee_code) {
      const checkEmployeeCode = updateData.employee_code || oldData.employee_code;
      const checkStartDate = updateData.start_date || oldData.start_date;
      const checkEndDate = updateData.end_date || oldData.end_date;

      const [conflictingEvaluations] = await pool.promise().query(
        `SELECT id, evaluation_type, start_date, end_date, employee_name
         FROM evaluations
         WHERE employee_code = ?
         AND id != ?
         AND (
           (start_date <= ? AND end_date >= ?) OR
           (start_date <= ? AND end_date >= ?) OR
           (start_date >= ? AND end_date <= ?)
         )`,
        [
          checkEmployeeCode,
          id,  // استثناء التقييم الحالي من التحقق
          checkStartDate, checkStartDate,  // التحقق من تداخل تاريخ البداية
          checkEndDate, checkEndDate,      // التحقق من تداخل تاريخ النهاية
          checkStartDate, checkEndDate     // التحقق من احتواء الفترة الجديدة للفترة الموجودة
        ]
      );

      if (conflictingEvaluations.length > 0) {
        const conflictingEval = conflictingEvaluations[0];

        const formattedStartDate = formatDateForDisplay(conflictingEval.start_date);
        const formattedEndDate = formatDateForDisplay(conflictingEval.end_date);

        return res.status(400).json({
          error: `يوجد تقييم ${conflictingEval.evaluation_type} للموظف ${conflictingEval.employee_name} في فترة متداخلة من ${formattedStartDate} إلى ${formattedEndDate}. لا يمكن تحديث التقييم ليتداخل مع فترة زمنية موجودة.`
        });
      }
    }

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(id);

    await pool.promise().query(
      `UPDATE evaluations SET ${setClause} WHERE id = ?`,
      values
    );

    // تسجيل النشاط
    const newData = { ...oldData, ...updateData };

    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'القسم',
      evaluation_type: 'نوع التقييم',
      start_date: 'تاريخ البداية',
      end_date: 'تاريخ النهاية',
      score: 'الدرجة',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `تقييم الموظف: ${newData.employee_name}`,
      oldData,
      newData,
      fieldLabels
    );

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'evaluations',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث التقييم بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث التقييم:', error);
    res.status(500).json({ error: 'فشل في تحديث التقييم' });
  }
});

// حذف تقييم
router.delete('/:id', authenticateToken, checkPermission('can_delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // الحصول على بيانات التقييم قبل الحذف
    const [evaluationData] = await pool.promise().query(
      "SELECT * FROM evaluations WHERE id = ?",
      [id]
    );

    if (evaluationData.length === 0) {
      return res.status(404).json({ error: 'التقييم غير موجود' });
    }

    const evaluation = evaluationData[0];

    const [result] = await pool.promise().query(
      "DELETE FROM evaluations WHERE id = ?",
      [id]
    );

    // تنسيق التواريخ بالعربية
    const formatDateToArabic = (dateValue) => {
      if (!dateValue) return 'غير محدد';
      try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

        const arabicMonths = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        const day = date.getDate();
        const month = arabicMonths[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
      } catch (error) {
        return 'تاريخ غير صحيح';
      }
    };

    const formattedStartDate = formatDateToArabic(evaluation.start_date);
    const formattedEndDate = formatDateToArabic(evaluation.end_date);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'evaluations',
      record_id: id.toString(),
      message: `تم حذف تقييم ${evaluation.evaluation_type} للموظف: ${evaluation.employee_name} (كود: ${evaluation.employee_code}) - القسم: ${evaluation.department} - الدرجة: ${evaluation.score} - الفترة: من ${formattedStartDate} إلى ${formattedEndDate}`
    });

    res.json({ message: 'تم حذف التقييم بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف التقييم:', error);
    res.status(500).json({ error: 'فشل في حذف التقييم' });
  }
});

// الحصول على تقييمات موظف محدد
router.get('/employee/:employee_code', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { employee_code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE e.employee_code = ?
      ORDER BY e.created_at DESC, e.id DESC
    `, [employee_code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب تقييمات الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب تقييمات الموظف' });
  }
});

// الحصول على إحصائيات التقييمات
router.get('/statistics/overview', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { start_date, end_date } = req.query;
    
    let dateFilter = "";
    const params = [];
    
    if (start_date && end_date) {
      dateFilter = " WHERE start_date BETWEEN ? AND ?";
      params.push(start_date, end_date);
    }
    
    // إجمالي التقييمات
    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total_count, AVG(overall_score) as average_score FROM evaluations${dateFilter}`,
      params
    );
    
    // توزيع الدرجات
    const [scoreDistribution] = await pool.promise().query(
      `SELECT 
         CASE 
           WHEN overall_score >= 90 THEN 'ممتاز (90-100)'
           WHEN overall_score >= 80 THEN 'جيد جداً (80-89)'
           WHEN overall_score >= 70 THEN 'جيد (70-79)'
           WHEN overall_score >= 60 THEN 'مقبول (60-69)'
           ELSE 'ضعيف (أقل من 60)'
         END as score_range,
         COUNT(*) as count
       FROM evaluations${dateFilter}
       GROUP BY score_range
       ORDER BY MIN(overall_score) DESC`,
      params
    );
    
    // أفضل الموظفين
    const [topPerformers] = await pool.promise().query(
      `SELECT e.employee_code, emp.full_name as employee_name, AVG(e.overall_score) as average_score, COUNT(*) as evaluation_count
       FROM evaluations e
       LEFT JOIN employees emp ON e.employee_code = emp.code${dateFilter}
       GROUP BY e.employee_code, emp.full_name
       HAVING evaluation_count > 0
       ORDER BY average_score DESC
       LIMIT 10`,
      params
    );
    
    res.json({
      total: totalResult[0],
      score_distribution: scoreDistribution,
      top_performers: topPerformers
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات التقييمات:', error);
    res.status(500).json({ error: 'فشل في جلب إحصائيات التقييمات' });
  }
});

// البحث في التقييمات
router.get('/search', authenticateToken, checkPermission('view_evaluation'), async (req, res) => {
  try {
    const { 
      employee_code, 
      start_date, 
      end_date, 
      min_score, 
      max_score,
      evaluator,
      evaluation_period
    } = req.query;
    
    let query = `
      SELECT e.*, emp.full_name as employee_name, emp.department
      FROM evaluations e
      LEFT JOIN employees emp ON e.employee_code = emp.code
      WHERE 1=1
    `;
    const params = [];
    
    if (employee_code) {
      query += " AND e.employee_code = ?";
      params.push(employee_code);
    }
    
    if (start_date) {
      query += " AND e.start_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND e.end_date <= ?";
      params.push(end_date);
    }
    
    if (min_score) {
      query += " AND e.overall_score >= ?";
      params.push(min_score);
    }
    
    if (max_score) {
      query += " AND e.overall_score <= ?";
      params.push(max_score);
    }
    
    if (evaluator) {
      query += " AND e.evaluator LIKE ?";
      params.push(`%${evaluator}%`);
    }
    
    if (evaluation_period) {
      query += " AND e.evaluation_period = ?";
      params.push(evaluation_period);
    }
    
    query += " ORDER BY e.created_at DESC, e.id DESC";
    
    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في التقييمات:', error);
    res.status(500).json({ error: 'فشل في البحث في التقييمات' });
  }
});

module.exports = router;